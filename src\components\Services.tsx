import { mazius } from "@/font";
import clsx from "clsx";
import React from "react";
import Image from "next/image";

const Services = () => {
  return (
    <div className="main h-auto w-full bg-zinc-800">
      <h2 className={clsx("container mb-20 text-8xl text-white")}>
        My{" "}
        <span className={clsx("text-[#14CF93]", mazius.className)}>
          Services
        </span>
      </h2>
      <div className="main container divide-y divide-white text-5xl [&>div]:flex [&>div]:h-[200px] [&>div]:items-center [&>div]:justify-start [&>div]:text-white">
        <div className="elms">
          <h3>SEO Optimization</h3>
          <Image
            src={"/khizar-services/seo.png"}
            alt="seo"
            width={100}
            height={100}
          />
        </div>
        <div className="elms">
          <h3>Web Development</h3>
        </div>

        <div className="elms">
          <h3>Graphic Design</h3>
        </div>

        <div className="elms">
          <h3>AI & Automation</h3>
        </div>
      </div>
    </div>
  );
};

export default Services;
