{"name": "<PERSON><PERSON><PERSON>a-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.2", "@studio-freight/lenis": "^1.0.42", "autoprefixer": "^10.4.21", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "lightningcss": "^1.30.1", "locomotive-scroll": "^4.1.4", "next": "15.3.4", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/locomotive-scroll": "^4.1.4", "@types/next": "^8.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.11", "typescript": "^5"}}